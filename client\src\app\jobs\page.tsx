'use client'

import { useState, Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import { useQuery } from '@tanstack/react-query'
import { api, JobFilters } from '@/lib/api'
import { JobCardSkeleton } from '@/components/loading'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent } from '@/components/ui/card'
import { JobCard } from '@/components/jobs/job-card'
import { Search, Filter, X, Loader2, MapPin, Clock, Home, GraduationCap, DollarSign, Target, Building } from 'lucide-react'

function JobsContent() {
  const searchParams = useSearchParams()
  const [showFilters, setShowFilters] = useState(false)
  const [filters, setFilters] = useState<JobFilters>({
    search: searchParams.get('search') || '',
    category: searchParams.get('category') || '',
    location: searchParams.get('location') || '',
    workLocationType: searchParams.get('workLocationType') || '',
    jobType: searchParams.get('jobType') || '',
    experienceLevel: searchParams.get('experienceLevel') || '',
    salaryMin: searchParams.get('salaryMin') ? parseInt(searchParams.get('salaryMin')!) : undefined,
    salaryMax: searchParams.get('salaryMax') ? parseInt(searchParams.get('salaryMax')!) : undefined,
  })
  const [currentPage, setCurrentPage] = useState(1)

  const { data, isLoading, error } = useQuery({
    queryKey: ['jobs', filters, currentPage],
    queryFn: () => api.getJobs(filters, currentPage, 12),
  })

  if(!isLoading){
    console.log(data)
  }

  const { data: categoriesData } = useQuery({
    queryKey: ['job-categories'],
    queryFn: () => api.getJobCategories(),
  })

  const categories = categoriesData?.categories || []

  const jobTypes = [
    { value: 'FULL_TIME', label: 'Full Time' },
    { value: 'PART_TIME', label: 'Part Time' },
    { value: 'CONTRACT', label: 'Contract' },
    { value: 'INTERNSHIP', label: 'Internship' },
  ]

  const workTypes = [
    { value: 'ONSITE', label: 'On-site' },
    { value: 'REMOTE', label: 'Remote' },
    { value: 'HYBRID', label: 'Hybrid' },
  ]

  const experienceLevels = [
    { value: 'STUDENT', label: 'Student / Currently Studying' },
    { value: 'FRESHER', label: 'Fresher' },
    { value: 'INTERNSHIP_ONLY', label: 'Internship Experience Only' },
    { value: 'ZERO_TO_ONE_YEAR', label: '0–1 Year' },
    { value: 'ONE_TO_THREE_YEARS', label: '1–3 Years' },
    { value: 'THREE_TO_FIVE_YEARS', label: '3–5 Years' },
    { value: 'FIVE_PLUS_YEARS', label: '5+ Years' },
  ]

  const handleFilterChange = (key: keyof JobFilters, value: string | number | undefined) => {
    setFilters(prev => ({ ...prev, [key]: value === '' || value === 'all' ? undefined : value }))
    setCurrentPage(1)
  }

  const clearFilters = () => {
    setFilters({})
    setCurrentPage(1)
  }

  const hasActiveFilters = Object.values(filters).some(value => value !== undefined && value !== '')

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-muted/10 to-secondary/5">
      {/* Header */}
      <section className="relative py-20 lg:py-32 overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-background via-muted/20 to-secondary/10"></div>
        <div className="absolute top-20 left-10 w-72 h-72 bg-primary/10 rounded-full blur-3xl float"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-accent/10 rounded-full blur-3xl float" style={{animationDelay: '2s'}}></div>

        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="max-w-5xl mx-auto text-center">
            <div className="mb-8">
              <span className="inline-block px-4 py-2 bg-primary/10 text-primary font-semibold rounded-full text-sm mb-6">
                🌟 {data ? `${data.pagination.total} opportunities available` : 'Loading opportunities...'}
              </span>
            </div>
            <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold text-foreground mb-8 leading-tight">
              Professional{' '}
              <span className="gradient-primary bg-clip-text text-transparent">
                Opportunities
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-muted-foreground mb-12 max-w-3xl mx-auto leading-relaxed">
              Discover career opportunities at leading companies with excellent workplace cultures.
              <span className="text-foreground font-semibold"> Your next role awaits.</span>
            </p>
          </div>
        </div>
      </section>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search and Filters */}
        <div className="space-y-8 mb-12">
          {/* Search Bar */}
          <div className="relative max-w-3xl mx-auto">
            <div className="relative glass border-0 rounded-3xl overflow-hidden">
              <Search className="absolute left-6 top-1/2 transform -translate-y-1/2 text-primary h-6 w-6" />
              <Input
                placeholder="Search for opportunities, companies, or roles..."
                value={filters.search || ''}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="pl-16 pr-6 py-6 text-lg font-medium border-0 bg-transparent placeholder:text-muted-foreground/70 focus:ring-2 focus:ring-primary/50"
              />
            </div>
          </div>

          {/* Filter Toggle */}
          <div className="flex items-center justify-between max-w-4xl mx-auto">
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-3 rounded-2xl font-semibold px-6 py-3 glass border-0 hover:scale-105 transition-all duration-200"
            >
              <Filter className="h-5 w-5" />
              {showFilters ? 'Hide Filters' : 'Show Filters'}
              {hasActiveFilters && (
                <span className="bg-primary text-primary-foreground text-xs px-3 py-1 rounded-full font-bold">
                  {Object.values(filters).filter(v => v !== undefined && v !== '').length}
                </span>
              )}
            </Button>

            {hasActiveFilters && (
              <Button
                variant="ghost"
                onClick={clearFilters}
                className="flex items-center gap-2 rounded-2xl font-semibold hover:bg-destructive/10 hover:text-destructive transition-all duration-200"
              >
                <X className="h-4 w-4" />
                Clear All
              </Button>
            )}
          </div>

          {/* Collapsible Filters */}
          {showFilters && (
            <Card className="max-w-4xl mx-auto glass border-0 overflow-hidden">
              <CardContent className="p-8">
                <div className="mb-6 text-center">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <Target className="h-5 w-5 text-primary" />
                    <h3 className="text-xl font-bold text-foreground">Refine Your Search</h3>
                  </div>
                  <p className="text-muted-foreground">Use filters to find opportunities that match your preferences</p>
                </div>
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                  {/* Location */}
                  <div>
                    <label className="text-sm font-bold mb-3 text-foreground flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-primary" />
                      Location
                    </label>
                    <Input
                      placeholder="Where do you want to work?"
                      value={filters.location || ''}
                      onChange={(e) => handleFilterChange('location', e.target.value)}
                      className="rounded-2xl border-2 h-12 font-medium"
                    />
                  </div>

                  {/* Category */}
                  <div>
                    <label className="text-sm font-bold mb-3 text-foreground flex items-center gap-2">
                      <Building className="h-4 w-4 text-primary" />
                      Category
                    </label>
                    <Select value={filters.category || 'all'} onValueChange={(value) => handleFilterChange('category', value)}>
                      <SelectTrigger className="rounded-2xl border-2 h-12 font-medium">
                        <SelectValue placeholder="What field interests you?" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Categories</SelectItem>
                        {categories.map((category) => (
                          <SelectItem key={category} value={category}>
                            {category}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Job Type */}
                  <div>
                    <label className="text-sm font-bold mb-3 text-foreground flex items-center gap-2">
                      <Clock className="h-4 w-4 text-primary" />
                      Job Type
                    </label>
                    <Select value={filters.jobType || 'all'} onValueChange={(value) => handleFilterChange('jobType', value)}>
                      <SelectTrigger className="rounded-2xl border-2 h-12 font-medium">
                        <SelectValue placeholder="Full-time, part-time?" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Types</SelectItem>
                        {jobTypes.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Work Type */}
                  <div>
                    <label className="text-sm font-bold mb-3 text-foreground flex items-center gap-2">
                      <Home className="h-4 w-4 text-primary" />
                      Work Style
                    </label>
                    <Select value={filters.workLocationType || 'all'} onValueChange={(value) => handleFilterChange('workLocationType', value)}>
                      <SelectTrigger className="rounded-2xl border-2 h-12 font-medium">
                        <SelectValue placeholder="Remote, hybrid, onsite?" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Work Types</SelectItem>
                        {workTypes.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Experience Level */}
                  <div>
                    <label className="text-sm font-bold mb-3 text-foreground flex items-center gap-2">
                      <GraduationCap className="h-4 w-4 text-primary" />
                      Experience Level
                    </label>
                    <Select value={filters.experienceLevel || 'all'} onValueChange={(value) => handleFilterChange('experienceLevel', value)}>
                      <SelectTrigger className="rounded-2xl border-2 h-12 font-medium">
                        <SelectValue placeholder="How experienced are you?" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Levels</SelectItem>
                        {experienceLevels.map((level) => (
                          <SelectItem key={level.value} value={level.value}>
                            {level.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Salary Range */}
                  <div className="md:col-span-2 lg:col-span-1">
                    <label className="text-sm font-bold mb-3 text-foreground flex items-center gap-2">
                      <DollarSign className="h-4 w-4 text-primary" />
                      Salary Range (USD)
                    </label>
                    <div className="flex gap-3">
                      <Input
                        type="number"
                        placeholder="Min salary"
                        value={filters.salaryMin || ''}
                        onChange={(e) => handleFilterChange('salaryMin', e.target.value ? parseInt(e.target.value) : undefined)}
                        className="rounded-2xl border-2 h-12 font-medium"
                      />
                      <Input
                        type="number"
                        placeholder="Max salary"
                        value={filters.salaryMax || ''}
                        onChange={(e) => handleFilterChange('salaryMax', e.target.value ? parseInt(e.target.value) : undefined)}
                        className="rounded-2xl border-2 h-12 font-medium"
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Results */}
        {isLoading ? (
          <div className="grid gap-8 md:grid-cols-2 xl:grid-cols-3">
            {Array.from({ length: 6 }).map((_, i) => (
              <JobCardSkeleton key={i} />
            ))}
          </div>
        ) : error ? (
          <div className="text-center py-20">
            <div className="mb-6">
              <div className="mx-auto w-20 h-20 rounded-3xl bg-destructive/10 flex items-center justify-center mb-4">
                <X className="h-10 w-10 text-destructive" />
              </div>
              <h3 className="text-2xl font-bold text-foreground mb-2">Oops! Something went wrong</h3>
              <p className="text-muted-foreground text-lg mb-6">Failed to load jobs. Please try again.</p>
            </div>
            <Button onClick={() => window.location.reload()} className="rounded-2xl font-bold px-8 py-3">
              Try Again
            </Button>
          </div>
        ) : !data?.jobs?.length ? (
          <div className="text-center py-20">
            <div className="mb-6">
              <div className="mx-auto w-20 h-20 rounded-3xl bg-muted/50 flex items-center justify-center mb-4">
                <Search className="h-10 w-10 text-muted-foreground" />
              </div>
              <h3 className="text-2xl font-bold text-foreground mb-2">No jobs found</h3>
              <p className="text-muted-foreground text-lg mb-6">Try adjusting your filters or search terms.</p>
            </div>
            <Button variant="outline" onClick={clearFilters} className="rounded-2xl font-bold px-8 py-3">
              Clear Filters
            </Button>
          </div>
        ) : (
          <>
            <div className="mb-8">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-foreground flex items-center gap-2">
                  <Target className="h-6 w-6 text-primary" />
                  {data.pagination.total} opportunities found
                </h2>
                <span className="text-muted-foreground font-medium">
                  Page {currentPage} of {data.pagination.totalPages}
                </span>
              </div>
            </div>
            <div className="grid gap-8 md:grid-cols-2 xl:grid-cols-3">
              {data.jobs.map((job) => (
                <JobCard key={job.id} job={job} />
              ))}
            </div>

            {/* Pagination */}
            {data.pagination.totalPages > 1 && (
              <div className="mt-16 flex items-center justify-center">
                <div className="glass rounded-3xl p-2 flex items-center space-x-2">
                  <Button
                    variant="ghost"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={!data.pagination.hasPrev}
                    className="rounded-2xl font-semibold px-6 py-3 disabled:opacity-50"
                  >
                    Previous
                  </Button>

                  <div className="flex items-center space-x-1">
                    {Array.from({ length: Math.min(5, data.pagination.totalPages) }, (_, i) => {
                      const page = i + 1
                      return (
                        <Button
                          key={page}
                          variant={currentPage === page ? "default" : "ghost"}
                          size="sm"
                          onClick={() => handlePageChange(page)}
                          className={`rounded-2xl font-bold w-12 h-12 ${
                            currentPage === page
                              ? 'gradient-primary text-primary-foreground'
                              : 'hover:bg-muted/50'
                          }`}
                        >
                          {page}
                        </Button>
                      )
                    })}
                  </div>

                  <Button
                    variant="ghost"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={!data.pagination.hasNext}
                    className="rounded-2xl font-semibold px-6 py-3 disabled:opacity-50"
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}

export default function JobsPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="flex items-center">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          <span className="ml-3 text-muted-foreground">Loading jobs...</span>
        </div>
      </div>
    }>
      <JobsContent />
    </Suspense>
  )
}
